import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export interface ReorderTargetsPayload {
  targetOrders: Array<{
    uid: string;
    order: number;
  }>;
}

export interface ReorderTargetsResponse {
  success: boolean;
  message?: string;
}

class TargetsService {
  async reorderTargets(
    payload: ReorderTargetsPayload,
  ): Promise<ReorderTargetsResponse> {
    try {
      const response = await axios.patch<ReorderTargetsResponse>(
        `${BASE_URL}/targets/order-list`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      return response.data;
    } catch (error) {
      console.error('Error reordering targets:', error);
      throw error;
    }
  }
}

const targetsService = new TargetsService();

export default targetsService;
