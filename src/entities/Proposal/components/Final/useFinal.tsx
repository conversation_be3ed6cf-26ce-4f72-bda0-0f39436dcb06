import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';
import { arrayMove } from '@dnd-kit/sortable';

import { useCatalog } from '~/entities/Home/hooks/useCatalog';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { useAgreeStatus } from '~/shared/hooks/useAgreeStatus';
import proposalService from '~/shared/services/proposal';
import targetTypesService from '~/shared/services/targetTypes';
import targetsService from '~/shared/services/targets';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { filterTargetsByType } from '../../utils/targetFilters';

import { CreateTargetBody, UseFinalParams, UseFinalReturn } from './types';

export function useFinal({
  proposalUid,
  targets,
  allTargets,
  catalogFilterHook,
  onProposalUpdate,
}: UseFinalParams): UseFinalReturn {
  const { t } = useTranslate();
  const actionModal = useActionModal();
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  // States
  const [draggedItem, setDraggedItem] = useState<
    DeliverableItem | Target | null
  >(null);
  const [selectedTargets, setSelectedTargets] = useState<Target[]>(
    filterTargetsByType(targets || [], TargetTypeEnum.FINAL),
  );
  const [acceptedTargetUids, setAcceptedTargetUids] = useState<string[]>([]);
  const [loadingTargetUids, setLoadingTargetUids] = useState<string[]>([]);
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false);
  const [drawerDeliverable, setDrawerDeliverable] = useState<
    DeliverableItem | Target
  >();
  const [isEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const [drawerType, setDrawerType] = useState<'draft' | 'target' | null>(null);

  const [localAgreeStatusOverrides, setLocalAgreeStatusOverrides] = useState<
    Record<string, boolean>
  >({});

  const [recentlyMergedTargets, setRecentlyMergedTargets] = useState<
    Set<string>
  >(new Set());

  // Reordering state
  const [isReordering, setIsReordering] = useState(false);
  const [previousTargetOrder, setPreviousTargetOrder] = useState<Target[]>([]);

  useEffect(() => {
    const finalTargets = filterTargetsByType(
      targets || [],
      TargetTypeEnum.FINAL,
    );
    setSelectedTargets(finalTargets);
  }, [targets]);

  const { filters } = catalogFilterHook;

  const { data: deliverables } = useCatalog(1, 100, undefined, filters);

  const { getTargetAgreeStatus: originalGetTargetAgreeStatus } = useAgreeStatus(
    {
      targets: allTargets,
      currentTargetType: TargetTypeEnum.FINAL,
      enabled: true,
    },
  );

  const getTargetAgreeStatus = useCallback(
    (targetUid: string): boolean | null => {
      if (localAgreeStatusOverrides[targetUid] !== undefined) {
        return localAgreeStatusOverrides[targetUid];
      }
      return originalGetTargetAgreeStatus(targetUid);
    },
    [localAgreeStatusOverrides, originalGetTargetAgreeStatus],
  );

  const totalWeight = useMemo(
    () =>
      selectedTargets
        .filter(target => {
          if (!target.uid) {
            return true;
          }
          const agreeStatus = getTargetAgreeStatus(target.uid);
          return agreeStatus !== false;
        })
        .reduce((sum, target) => sum + (target.weight || 0), 0),
    [selectedTargets, getTargetAgreeStatus],
  );

  useEffect(() => {
    Object.keys(localAgreeStatusOverrides).forEach(targetUid => {
      const serverStatus = originalGetTargetAgreeStatus(targetUid);
      const localStatus = localAgreeStatusOverrides[targetUid];

      if (serverStatus === localStatus) {
        setLocalAgreeStatusOverrides(prev => {
          const newOverrides = { ...prev };
          delete newOverrides[targetUid];
          return newOverrides;
        });
      }
    });
  }, [localAgreeStatusOverrides, originalGetTargetAgreeStatus]);

  useEffect(() => {
    recentlyMergedTargets.forEach(targetUid => {
      const agreeStatus = originalGetTargetAgreeStatus(targetUid);
      if (agreeStatus !== null && agreeStatus !== undefined) {
        setRecentlyMergedTargets(prev => {
          const newSet = new Set(prev);
          newSet.delete(targetUid);
          return newSet;
        });
      }
    });
  }, [recentlyMergedTargets, originalGetTargetAgreeStatus]);

  const { isLoading, mutate: mutateCompleted } = useMutation({
    mutationFn: () => {
      return proposalService.changeProposalStatus(
        proposalUid,
        ProposalStatusEnum.COMPLETED,
        session?.user.token,
        session?.user.sessionToken,
      );
    },
    onSuccess: (response: Proposal) => {
      onProposalUpdate?.(response);
    },
    onError: error => {
      console.error(error);
    },
  });

  const { isLoading: isLoadingDelete, mutate: mutateDelete } = useMutation({
    mutationFn: (targets: Target[]) => {
      const targetsToDelete = targets.map(target => {
        return { uid: target.uid || '', targetType: TargetTypeEnum.FINAL };
      });

      return proposalService.deleteTargets(
        proposalUid,
        targetsToDelete,
        session?.user.token,
        session?.user.sessionToken,
      );
    },
    onSuccess: (proposal: Proposal) => {
      const finalTargets = filterTargetsByType(
        proposal.targets || [],
        TargetTypeEnum.FINAL,
      );
      setSelectedTargets(finalTargets);
      actionModal.closeModal();
    },
    onError: error => {
      console.error(error);
    },
  });

  const getChildForBodyRequest = (child: Target[]) => {
    return child.map(c => ({
      weight: c.weight,
      scope: c.scope,
      uidDeliverable: c.deliverable?.uid || '',
      ...(c.uid && { uid: c.uid }),
    }));
  };

  const { isLoading: isLoadingMergeTargets, mutate: mutateMergeTargets } =
    useMutation({
      mutationFn: (values: { firstTarget: Target; secondTarget: Target }) => {
        const firstTarget = values.firstTarget as Target;
        const secondTarget = values.secondTarget as Target;

        const treatedChildren = [];

        if (firstTarget.children && firstTarget.children.length > 0) {
          treatedChildren.push(...getChildForBodyRequest(firstTarget.children));
        } else {
          treatedChildren.push({
            weight: firstTarget.weight,
            scope: firstTarget.scope,
            uidDeliverable:
              firstTarget.uidDeliverable || firstTarget.deliverable?.uid || '',
            ...(firstTarget.uid && { uid: firstTarget.uid }),
          });
        }

        if (secondTarget.children && secondTarget.children.length > 0) {
          treatedChildren.push(
            ...getChildForBodyRequest(secondTarget.children),
          );
        } else {
          treatedChildren.push({
            weight: secondTarget.weight,
            scope: secondTarget.scope,
            uidDeliverable:
              secondTarget.uidDeliverable ||
              secondTarget.deliverable?.uid ||
              '',
            ...(secondTarget.uid && { uid: secondTarget.uid }),
          });
        }

        const data: CreateTargetBody = {
          targets: [
            {
              weight: (firstTarget.weight || 0) + (secondTarget.weight || 0),
              targetType: TargetTypeEnum.FINAL,
              children: treatedChildren,
            },
          ],
        };

        return proposalService.createTarget(
          proposalUid,
          data,
          session?.user.token,
          session?.user.sessionToken,
        );
      },
      onSuccess: (proposal: Proposal) => {
        const finalTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FINAL,
        );

        const newTargetUids = finalTargets
          .filter(
            target =>
              target.uid &&
              !selectedTargets.some(existing => existing.uid === target.uid),
          )
          .map(target => target.uid!)
          .filter(Boolean);

        if (newTargetUids.length > 0) {
          setRecentlyMergedTargets(
            prev => new Set([...prev, ...newTargetUids]),
          );

          setTimeout(() => {
            setRecentlyMergedTargets(prev => {
              const newSet = new Set(prev);
              newTargetUids.forEach(uid => newSet.delete(uid));
              return newSet;
            });
          }, 30000);
        }

        setSelectedTargets(finalTargets);
        actionModal.closeModal();

        void queryClient.invalidateQueries({
          predicate: query => query.queryKey[0] === 'agreeStatuses',
          refetchType: 'active',
        });

        void queryClient.refetchQueries({
          predicate: query => query.queryKey[0] === 'agreeStatuses',
          type: 'active',
        });

        onProposalUpdate?.(proposal);
      },
      onError: error => {
        console.error(error);
      },
    });

  const { isLoading: isLoadingAcceptTargets, mutate: mutateAcceptTargets } =
    useMutation({
      mutationFn: (payload: { targetUids: string[]; agree?: boolean }) => {
        return targetTypesService.addFinalToTargets(
          payload.targetUids,
          session?.user.token,
          session?.user.sessionToken,
          payload.agree,
        );
      },
      onSuccess: (response, payload) => {
        const targetUid = payload.targetUids[0];
        const targetToAccept = allTargets.find(
          target => target.uid === targetUid,
        );

        if (targetToAccept) {
          const finalTarget: Target = {
            ...targetToAccept,
            targetTypes: [
              ...(targetToAccept.targetTypes || []),
              { type: TargetTypeEnum.FINAL },
            ],
            children:
              targetToAccept.children?.map(child => ({
                ...child,
                targetTypes: child.targetTypes?.some(
                  tt => tt.type === TargetTypeEnum.FINAL,
                )
                  ? child.targetTypes
                  : [
                      ...(child.targetTypes || []),
                      { type: TargetTypeEnum.FINAL },
                    ],
              })) || [],
          };

          setSelectedTargets(prev => {
            const existingTargetIndex = prev.findIndex(
              t => t.uid === targetUid,
            );

            if (existingTargetIndex >= 0) {
              const updatedTargets = [...prev];
              updatedTargets[existingTargetIndex] = finalTarget;
              return filterTargetsByType(updatedTargets, TargetTypeEnum.FINAL);
            } else {
              const updatedTargets = [...prev, finalTarget];
              return filterTargetsByType(updatedTargets, TargetTypeEnum.FINAL);
            }
          });

          setAcceptedTargetUids(prev => [...prev, targetUid]);
        }

        setLoadingTargetUids(prev => prev.filter(uid => uid !== targetUid));

        if (payload.agree !== undefined) {
          setLocalAgreeStatusOverrides(prev => ({
            ...prev,
            [targetUid]: payload.agree!,
          }));
        }

        void queryClient.invalidateQueries({
          predicate: query => query.queryKey[0] === 'agreeStatuses',
          refetchType: 'active',
        });

        void queryClient.refetchQueries({
          predicate: query => query.queryKey[0] === 'agreeStatuses',
          type: 'active',
        });
      },
      onError: (error, payload) => {
        console.error('Error accepting targets:', error);
        const targetUid = payload.targetUids[0];
        setLoadingTargetUids(prev => prev.filter(uid => uid !== targetUid));
      },
    });

  const {
    isLoading: isLoadingAcceptAllTargets,
    mutate: mutateAcceptAllTargets,
  } = useMutation({
    mutationFn: (payload: { targetUids: string[]; agree?: boolean }) => {
      return targetTypesService.addFinalToTargets(
        payload.targetUids,
        session?.user.token,
        session?.user.sessionToken,
        payload.agree,
      );
    },
    onSuccess: (_, payload) => {
      const targetsToAccept = allTargets.filter(target =>
        payload.targetUids.includes(target.uid || ''),
      );

      const finalTargets = targetsToAccept.map(targetToAccept => ({
        ...targetToAccept,
        targetTypes: [
          ...(targetToAccept.targetTypes || []),
          { type: TargetTypeEnum.FINAL },
        ],
        children:
          targetToAccept.children?.map(child => ({
            ...child,
            targetTypes: child.targetTypes?.some(
              tt => tt.type === TargetTypeEnum.FINAL,
            )
              ? child.targetTypes
              : [...(child.targetTypes || []), { type: TargetTypeEnum.FINAL }],
          })) || [],
      }));

      setSelectedTargets(prev => {
        const updatedTargets = [...prev];

        finalTargets.forEach(finalTarget => {
          const existingTargetIndex = updatedTargets.findIndex(
            t => t.uid === finalTarget.uid,
          );

          if (existingTargetIndex >= 0) {
            updatedTargets[existingTargetIndex] = finalTarget;
          } else {
            updatedTargets.push(finalTarget);
          }
        });

        return filterTargetsByType(updatedTargets, TargetTypeEnum.FINAL);
      });

      setAcceptedTargetUids(prev => [...prev, ...payload.targetUids]);
      setLoadingTargetUids(prev =>
        prev.filter(uid => !payload.targetUids.includes(uid)),
      );

      if (payload.agree !== undefined) {
        setLocalAgreeStatusOverrides(prev => {
          const newOverrides = { ...prev };
          payload.targetUids.forEach(uid => {
            newOverrides[uid] = payload.agree!;
          });
          return newOverrides;
        });
      }

      void queryClient.invalidateQueries({
        predicate: query => query.queryKey[0] === 'agreeStatuses',
        refetchType: 'active',
      });

      void queryClient.refetchQueries({
        predicate: query => query.queryKey[0] === 'agreeStatuses',
        type: 'active',
      });
    },
    onError: (error, payload) => {
      console.error('Error accepting all targets:', error);
      setLoadingTargetUids(prev =>
        prev.filter(uid => !payload.targetUids.includes(uid)),
      );
    },
  });

  // Reordering mutation
  const { isLoading: isLoadingReorder, mutate: mutateReorderTargets } =
    useMutation({
      mutationFn: async (reorderedTargets: Target[]) => {
        const targetOrders = reorderedTargets
          .map((target, index) => ({
            uid: target.uid!,
            order: index + 1,
          }))
          .filter(item => item.uid);

        return targetsService.reorderTargets({ targetOrders });
      },
      onSuccess: () => {
        // Clear the previous order backup on successful reorder
        setPreviousTargetOrder([]);
        setIsReordering(false);
      },
      onError: (error) => {
        console.error('Error reordering targets:', error);
        // Rollback to previous order on error
        if (previousTargetOrder.length > 0) {
          setSelectedTargets(previousTargetOrder);
          setPreviousTargetOrder([]);
        }
        setIsReordering(false);
      },
    });

  const availableDeliverables = useMemo(() => {
    return deliverables?.data ?? [];
  }, [deliverables]);

  const selectedFinalTargetUids = useMemo(() => {
    const finalTargets = filterTargetsByType(
      selectedTargets,
      TargetTypeEnum.FINAL,
    );

    return new Set(
      finalTargets.flatMap(target => {
        const uids = [];
        if (target.uid) {
          uids.push(target.uid);
        }
        if (target.deliverable?.uid) {
          uids.push(target.deliverable.uid);
        }
        if (target.children && target.children.length > 0) {
          target.children.forEach(child => {
            if (child.uid) {
              uids.push(child.uid);
            }
            if (child.deliverable?.uid) {
              uids.push(child.deliverable.uid);
            }
          });
        }
        return uids;
      }),
    );
  }, [selectedTargets]);

  // Handlers
  const handleAcceptTarget = (targetUid: string, agree?: boolean) => {
    if (
      loadingTargetUids.includes(targetUid) ||
      acceptedTargetUids.includes(targetUid)
    ) {
      return;
    }

    const targetToAccept = allTargets.find(target => target.uid === targetUid);

    if (targetToAccept) {
      setLoadingTargetUids(prev => [...prev, targetUid]);
      mutateAcceptTargets({ targetUids: [targetUid], agree });
    }
  };

  const handleDragStart = (event: any) => {
    const item = event.active.data.current?.data;

    if (item) {
      setDraggedItem(item);
      const isFromSideList = availableDeliverables.some(
        d => d.uid === item.uid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    // Check if this is a reorder operation
    if (active.id && active.id.toString().startsWith('reorder-')) {
      if (over && over.id && over.id.toString().startsWith('reorder-drop-')) {
        // Dropping on a drop zone
        const activeTargetId = active.id.toString().replace('reorder-', '');
        const dropZoneTargetId = over.id.toString().replace('reorder-drop-', '');

        const oldIndex = selectedTargets.findIndex(target => target.uid === activeTargetId);
        let newIndex = selectedTargets.findIndex(target => target.uid === dropZoneTargetId);

        if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
          // If dropping after the current position, adjust the index
          if (oldIndex < newIndex) {
            newIndex = newIndex - 1;
          }

          const reorderedTargets = arrayMove(selectedTargets, oldIndex, newIndex);
          handleReorderTargets(reorderedTargets);
        }
      } else if (over && over.id && over.id.toString().startsWith('reorder-')) {
        // Dropping on another card
        const activeTargetId = active.id.toString().replace('reorder-', '');
        const overTargetId = over.id.toString().replace('reorder-', '');

        if (activeTargetId !== overTargetId) {
          const oldIndex = selectedTargets.findIndex(target => target.uid === activeTargetId);
          const newIndex = selectedTargets.findIndex(target => target.uid === overTargetId);

          if (oldIndex !== -1 && newIndex !== -1) {
            const reorderedTargets = arrayMove(selectedTargets, oldIndex, newIndex);
            handleReorderTargets(reorderedTargets);
          }
        }
      }
    } else if (over && over.id === 'selection-area') {
      const deliverable = active.data.current?.data;

      if (deliverable) {
        // TODO: improve this using a hook or context?
        setIsEditDrawer(false);
        setDrawerDeliverable(deliverable);
        setDrawerType('target');
        setIsOpenDrawer(true);
      }
    } else if (over && over.id.toString().includes('target')) {
      const firstTarget = over.data.current?.target as Target;
      const secondTarget = active.data.current?.data as Target;

      if (firstTarget && secondTarget && firstTarget.uid !== secondTarget.uid) {
        const isFirstTargetAgreed = isTargetAgreed(firstTarget);
        const isSecondTargetAgreed = isTargetAgreed(secondTarget);

        if (isFirstTargetAgreed || isSecondTargetAgreed) {
          actionModal.openModal({
            title: t('common_merge_not_allowed'),
            message: t('common_cannot_merge_agreed_targets'),
            actions: [
              {
                label: t('common_ok'),
                onClick: actionModal.closeModal,
                variant: 'primary',
              },
            ],
          });
          return;
        }

        actionModal.openModal({
          title: t('common_merge_targets'),
          message: t('common_do_you_really_want_to_merge_the_targets'),
          actions: [
            {
              label: t('common_yes'),
              onClick: () => {},
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ],
          onConfirm: async () => {
            const values = { firstTarget, secondTarget };
            await mutateMergeTargets(values);
          },
        });
      }
    }

    setDraggedItem(null);
    setIsDraggingNewItem(false);
  };

  const onDrawerSuccessSubmit = (proposal: Proposal) => {
    const finalTargets = filterTargetsByType(
      proposal.targets || [],
      TargetTypeEnum.FINAL,
    );
    setSelectedTargets(finalTargets);
    // Reset drawer state after successful submission
    setIsOpenDrawer(false);
    setIsEditDrawer(false);
    setDrawerDeliverable(undefined);
    setDrawerType(null);
  };

  const handleSubmit = () => {
    mutateCompleted();
  };

  const handleClearDeliverable = () => {
    actionModal.openModal({
      title: t('common_clear_list'),
      message: t('common_do_you_really_want_to_clear_the_list'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }

        const targetsToDelete = selectedTargets.map(target => ({
          uid: target.uid || '',
          targetType: TargetTypeEnum.FINAL,
        }));

        const deletedTargetUids = selectedTargets
          .map(target => target.uid)
          .filter(uid => uid) as string[];
        setAcceptedTargetUids(prevAccepted =>
          prevAccepted.filter(uid => !deletedTargetUids.includes(uid)),
        );

        const proposal = await proposalService.deleteTargets(
          proposalUid,
          targetsToDelete,
          session?.user.token,
          session?.user.sessionToken,
        );

        const finalTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FINAL,
        );
        setSelectedTargets(finalTargets);
        actionModal.closeModal();
      },
    });
  };

  const handleRemoveTargets = (targets: Target[]) => {
    const targetsToDelete = targets.map(target => ({
      uid: target.uid || '',
      targetType: TargetTypeEnum.FINAL,
    }));

    actionModal.openModal({
      title: t('common_remove_targets'),
      message: t('common_do_you_really_want_to_remove_the_targets'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }

        const removedTargetUids = targets.map(target => target.uid);

        const proposal = await proposalService.deleteTargets(
          proposalUid,
          targetsToDelete,
          session?.user.token,
          session?.user.sessionToken,
        );

        const finalTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FINAL,
        );
        setSelectedTargets(finalTargets);

        setAcceptedTargetUids(prev =>
          prev.filter(uid => !removedTargetUids.includes(uid)),
        );

        actionModal.closeModal();
      },
    });
  };

  const handleOpenEditDrawer = (target: Target) => {
    setIsEditDrawer(true);
    setDrawerDeliverable(target);
    setDrawerType('target');
    setIsOpenDrawer(true);
  };

  const isTargetLoading = (targetUid: string) => {
    return loadingTargetUids.includes(targetUid);
  };

  const isTargetAgreed = useCallback(
    (target: Target): boolean => {
      if (!target.uid) {
        return false;
      }

      if (localAgreeStatusOverrides[target.uid] !== undefined) {
        return true;
      }

      const agreeStatus = getTargetAgreeStatus(target.uid);
      return agreeStatus !== null && agreeStatus !== undefined;
    },
    [getTargetAgreeStatus, localAgreeStatusOverrides],
  );

  const handleCreateDraft = () => {
    setIsEditDrawer(false);
    setDrawerDeliverable(undefined);
    setDrawerType('draft');
    setIsOpenDrawer(true);
  };

  const areAllTargetsAgreed = useMemo(() => {
    const proposalTargets = allTargets.filter(target =>
      target.targetTypes?.some(tt => tt.type === TargetTypeEnum.PROPOSAL),
    );

    if (proposalTargets.length === 0) {
      return true;
    }

    return proposalTargets.every(target => {
      if (!target.uid) {
        return false;
      }
      const agreeStatus = getTargetAgreeStatus(target.uid);
      return agreeStatus !== null && agreeStatus !== undefined;
    });
  }, [allTargets, getTargetAgreeStatus]);

  const handleFillWithProposal = () => {
    const proposalTargets = filterTargetsByType(
      allTargets,
      TargetTypeEnum.PROPOSAL,
    );

    const currentFinalTargetUids = new Set(
      selectedTargets.map(target => target.uid).filter(Boolean),
    );

    const targetUidsToAccept = proposalTargets
      .filter(
        target =>
          target.uid &&
          !acceptedTargetUids.includes(target.uid) &&
          !currentFinalTargetUids.has(target.uid),
      )
      .map(target => target.uid) as string[];

    if (targetUidsToAccept.length > 0) {
      setLoadingTargetUids(prev => [...prev, ...targetUidsToAccept]);
      mutateAcceptAllTargets({ targetUids: targetUidsToAccept, agree: true });
    }
  };

  const handleReorderTargets = (reorderedTargets: Target[]) => {
    // Store the current order for potential rollback
    setPreviousTargetOrder([...selectedTargets]);

    // Optimistically update the UI
    setSelectedTargets(reorderedTargets);
    setIsReordering(true);

    // Call the API to persist the new order
    mutateReorderTargets(reorderedTargets);
  };

  const handleFillWithFeedback = () => {
    const feedbackTargets = filterTargetsByType(
      allTargets,
      TargetTypeEnum.FEEDBACK,
    );

    const currentFinalTargetUids = new Set(
      selectedTargets.map(target => target.uid).filter(Boolean),
    );

    const targetUidsToAccept = feedbackTargets
      .filter(
        target =>
          target.uid &&
          !acceptedTargetUids.includes(target.uid) &&
          !currentFinalTargetUids.has(target.uid),
      )
      .map(target => target.uid) as string[];

    if (targetUidsToAccept.length > 0) {
      setLoadingTargetUids(prev => [...prev, ...targetUidsToAccept]);
      mutateAcceptAllTargets({ targetUids: targetUidsToAccept, agree: true });
    }
  };

  return {
    // States
    selectedTargets,
    acceptedTargetUids,
    loadingTargetUids,
    draggedItem,
    isDraggingNewItem,
    isOpenDrawer,
    isEditDrawer,
    drawerDeliverable,
    drawerType,
    totalWeight,

    // Loading states
    isLoading,
    isLoadingDelete,
    isLoadingMergeTargets,
    isLoadingAcceptTargets,
    isLoadingAcceptAllTargets,
    isLoadingReorder,

    // Handlers
    handleAcceptTarget,
    handleDragStart,
    handleDragEnd,
    handleSubmit,
    handleClearDeliverable,
    handleRemoveTargets,
    onDrawerSuccessSubmit,
    setIsOpenDrawer,
    setDrawerDeliverable,
    setDrawerType,
    handleOpenEditDrawer,
    handleCreateDraft,
    handleFillWithProposal,
    handleFillWithFeedback,
    handleReorderTargets,

    // Computed values
    availableDeliverables,
    selectedFinalTargetUids,

    // Helper functions
    filterTargetsByType,
    isTargetLoading,
    getTargetAgreeStatus,
    isTargetAgreed,
    areAllTargetsAgreed,

    // Modal control
    actionModal,
  };
}
