import React from 'react';
import { Trash } from 'react-bootstrap-icons';
import {
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  Button,
  FooterActionBar,
  Tooltip,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { CatalogCard } from '~/entities/Home/components';
import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { ChildCard, TargetCard } from '~/shared/components';
import { ActionModal } from '~/shared/components/ActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { CreateDraftDrawer } from '../CreateDraftDrawer';
import { CatalogWithTabs } from '../Feedback/CatalogWithTabs';
import { CreateTargetDrawer } from '../Proposal/CreateTargetDrawer';
import { DroppableTarget } from '../Proposal/DroppableTarget';
import { SortableTargetCard } from '../Proposal/SortableTargetCard';
import { SortableTargetsList } from '../Proposal/SortableTargetsList';

import { FinalDragProps } from './types';
import { useFinal } from './useFinal';

export function FinalDrag({
  proposalStatus,
  proposalUid,
  targets,
  allTargets = [],
  onProposalUpdate,
}: FinalDragProps) {
  const { t } = useTranslate();
  const catalogFilterHook = useCatalogFilter();

  const {
    selectedTargets,
    acceptedTargetUids,
    draggedItem,
    isDraggingNewItem,
    isOpenDrawer,
    drawerDeliverable,
    drawerType,
    isLoading,
    isLoadingDelete,
    isLoadingMergeTargets,
    isLoadingAcceptAllTargets,
    handleAcceptTarget,
    handleDragStart,
    handleDragEnd,
    handleSubmit,
    handleClearDeliverable,
    handleRemoveTargets,
    handleFillWithProposal,
    handleFillWithFeedback,
    onDrawerSuccessSubmit,
    setIsOpenDrawer,
    setDrawerType,
    availableDeliverables,
    selectedFinalTargetUids,
    isTargetLoading,
    getTargetAgreeStatus,
    isTargetAgreed,
    areAllTargetsAgreed,
    actionModal,
    totalWeight,
    isEditDrawer,
    handleOpenEditDrawer,
    handleCreateDraft,
    handleReorderTargets,
    isLoadingReorder,
  } = useFinal({
    proposalUid,
    targets,
    allTargets,
    catalogFilterHook,
    onProposalUpdate,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  return (
    <>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex h-[calc(100vh-340px)] gap-6">
          <CatalogWithTabs
            deliverables={availableDeliverables}
            proposalTargets={allTargets}
            isInitialLoading={false}
            isSearchLoading={false}
            isError={false}
            catalogFilterHook={catalogFilterHook}
            onAcceptTarget={handleAcceptTarget}
            acceptedTargetUids={[
              ...acceptedTargetUids,
              ...Array.from(selectedFinalTargetUids),
            ]}
            isTargetLoading={isTargetLoading}
            getTargetAgreeStatus={getTargetAgreeStatus}
            showProposalTargetType={TargetTypeEnum.PROPOSAL}
            showFeedbackTab={true}
            showBadgesInProposalTab={true}
            filterAcceptedFromFeedbackTab={false}
          />
          <SortableTargetsList
            selectedData={selectedTargets}
            isDraggingNewItem={isDraggingNewItem}
            onReorder={handleReorderTargets}
            isReorderingDisabled={isLoadingReorder}
            actions={[
              {
                label: t('common_create_draft'),
                onClick: handleCreateDraft,
                variant: 'secondary',
              },
              {
                label: t('common_clear_list'),
                onClick: handleClearDeliverable,
                variant: 'secondary',
                iconLeft: <Trash />,
                disabled: selectedTargets.length === 0,
              },
              {
                label: t('common_fill_list_with_proposal'),
                onClick: handleFillWithProposal,
                variant: 'secondary',
                disabled: isLoadingAcceptAllTargets || isLoadingDelete,
              },
              {
                label: t('common_fill_list_with_feedback'),
                onClick: handleFillWithFeedback,
                variant: 'secondary',
                disabled: isLoadingAcceptAllTargets || isLoadingDelete,
              },
            ]}
          >
            {selectedTargets
              .filter(target => {
                if (!target.uid) {
                  return true;
                }
                const agreeStatus = getTargetAgreeStatus(target.uid);
                return agreeStatus !== false;
              })
              .map(target => {
                if (target.children && target.children.length > 1) {
                  const isAgreed = isTargetAgreed(target);
                  return (
                    <SortableTargetCard
                      key={target.uid}
                      id={target.uid || ''}
                      isReorderingDisabled={isLoadingReorder}
                      isDraggingNewItem={isDraggingNewItem}
                    >
                      <DroppableTarget
                        isDragging={isDraggingNewItem}
                        target={target}
                        disabled={isAgreed}
                      >
                        <TargetCard
                          key={target.uid}
                          data={target}
                          proposalStatus={proposalStatus}
                          hideChildren={target.children.length <= 1}
                          currentTargetType={TargetTypeEnum.FINAL}
                          onRemoveActionClick={handleRemoveTargets}
                          onEditTarget={handleOpenEditDrawer}
                          isOnDroppableArea
                          isTargetAgreed={isTargetAgreed}
                        />
                      </DroppableTarget>
                    </SortableTargetCard>
                  );
                }
                const isAgreed = isTargetAgreed(target);
                return (
                  <SortableTargetCard
                    key={target.uid}
                    id={target.uid || ''}
                    isReorderingDisabled={isLoadingReorder}
                    isDraggingNewItem={isDraggingNewItem}
                  >
                    <DroppableTarget
                      isDragging={isDraggingNewItem}
                      target={target}
                      disabled={isAgreed}
                    >
                      <ChildCard
                        key={target.uid}
                        target={target}
                        disableDrag={isAgreed}
                        onRemoveActionClick={handleRemoveTargets}
                        onEditTarget={handleOpenEditDrawer}
                        showActions={!isAgreed}
                        disableChildActions={isAgreed}
                        isAgreedTarget={isAgreed}
                      />
                    </DroppableTarget>
                  </SortableTargetCard>
                );
              })}
          </SortableTargetsList>
        </div>
        <DragOverlay>
          {(draggedItem as Target)?.deliverable ? (
            <ChildCard target={draggedItem as Target} />
          ) : (
            <CatalogCard data={draggedItem as DeliverableItem} isDragging />
          )}
        </DragOverlay>
      </DndContext>
      <CreateDraftDrawer
        isOpen={isOpenDrawer && drawerType === 'draft'}
        onClose={() => {
          setIsOpenDrawer(false);
          setDrawerType(null);
        }}
        proposalId={proposalUid}
        onSuccessSubmit={onDrawerSuccessSubmit}
        targetType={TargetTypeEnum.FINAL}
      />
      <CreateTargetDrawer
        isOpen={isOpenDrawer && drawerType === 'target'}
        onClose={() => {
          setIsOpenDrawer(false);
          setDrawerType(null);
        }}
        data={drawerDeliverable}
        isEdit={isEditDrawer}
        proposalId={proposalUid}
        onSuccessSubmit={onDrawerSuccessSubmit}
        targetType={TargetTypeEnum.FINAL}
      />
      <ActionModal
        isOpen={actionModal.isOpen}
        openModal={actionModal.openModal}
        closeModal={actionModal.closeModal}
        title={actionModal.title}
        message={actionModal.message}
        actions={[
          {
            label: t('common_yes'),
            onClick: actionModal.handleConfirm,
            variant: 'primary',
            isLoading: isLoadingMergeTargets,
          },
          {
            label: t('common_no'),
            onClick: actionModal.closeModal,
            variant: 'secondary',
          },
        ]}
      />
      <FooterActionBar>
        <Button
          variant="secondary"
          border="default"
          className="w-fit"
          onClick={() => window.history.back()}
        >
          Back
        </Button>
        <Tooltip.Provider delayDuration={0}>
          <Tooltip.Root>
            <Tooltip.Trigger>
              <Button
                id="deliverable-form"
                isLoading={isLoading}
                variant="primary"
                className="w-fit"
                round="md"
                onClick={handleSubmit}
                disabled={totalWeight !== 100 || !areAllTargetsAgreed}
              >
                {t('common_submit')}
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content
              className={`z-[9999] break-words max-w-[90vw] ${
                totalWeight === 100 && areAllTargetsAgreed ? 'hidden' : ''
              }`}
              side="bottom"
              align="center"
            >
              <Typography variant="metadata-sm-regular">
                {totalWeight !== 100
                  ? `${t('common_total_weight_minimal_value')} ${totalWeight}%`
                  : !areAllTargetsAgreed
                  ? 'All proposal targets must have agreement status determined'
                  : ''}
              </Typography>
            </Tooltip.Content>
          </Tooltip.Root>
        </Tooltip.Provider>
      </FooterActionBar>
    </>
  );
}
