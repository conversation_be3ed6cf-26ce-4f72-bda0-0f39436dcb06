import React from 'react';
import { useDroppable } from '@dnd-kit/core';

export interface ReorderDropZoneProps {
  id: string;
  isActive?: boolean;
}

export function ReorderDropZone({ id, isActive = false }: ReorderDropZoneProps) {
  const { isOver, setNodeRef } = useDroppable({
    id: `reorder-drop-${id}`,
    data: {
      type: 'reorder-drop',
      targetId: id,
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={`h-2 transition-all duration-200 ${
        isOver || isActive
          ? 'bg-blue-200 border-2 border-dashed border-blue-400 h-8'
          : 'bg-transparent'
      }`}
      style={{ minHeight: isOver || isActive ? '32px' : '8px' }}
    />
  );
}
