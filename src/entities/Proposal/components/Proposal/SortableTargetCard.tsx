import React from 'react';
import { GripVertical } from 'react-bootstrap-icons';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

export interface SortableTargetCardProps {
  id: string;
  children: React.ReactNode;
  isReorderingDisabled?: boolean;
  isDraggingNewItem?: boolean;
}

export function SortableTargetCard({
  id,
  children,
  isReorderingDisabled = false,
  isDraggingNewItem = false,
}: SortableTargetCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: id,
    disabled: isReorderingDisabled || isDraggingNewItem,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 1,
  };

  // Don't show sortable functionality when disabled or dragging new items
  if (isReorderingDisabled || isDraggingNewItem) {
    return <div>{children}</div>;
  }

  return (
    <div ref={setNodeRef} style={style} className="relative">
      {/* Sortable drag handle - positioned on the left side */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100 transition-colors"
        style={{ touchAction: 'none' }}
      >
        <GripVertical size={16} className="text-gray-400 hover:text-gray-600" />
      </div>

      {/* Content with left padding to make room for drag handle */}
      <div className="pl-8">{children}</div>
    </div>
  );
}
