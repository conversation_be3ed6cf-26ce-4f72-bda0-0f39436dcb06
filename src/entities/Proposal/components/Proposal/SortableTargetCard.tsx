import React from 'react';
import { GripVertical } from 'react-bootstrap-icons';
import { useDraggable } from '@dnd-kit/core';

export interface SortableTargetCardProps {
  id: string;
  children: React.ReactNode;
  isReorderingDisabled?: boolean;
  isDraggingNewItem?: boolean;
}

export function SortableTargetCard({
  id,
  children,
  isReorderingDisabled = false,
  isDraggingNewItem = false,
}: SortableTargetCardProps) {
  // Use a special prefix to distinguish reorder operations from merge operations
  const reorderId = `reorder-${id}`;

  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: reorderId,
      disabled: isReorderingDisabled || isDraggingNewItem,
      data: {
        type: 'reorder',
        targetId: id,
      },
    });

  const style = {
    transform: transform
      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
      : undefined,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 1,
  };

  // Don't show sortable functionality when disabled or dragging new items
  if (isReorderingDisabled || isDraggingNewItem) {
    return <div>{children}</div>;
  }

  return (
    <div ref={setNodeRef} style={style} className="relative">
      {/* Sortable drag handle - positioned on the left side */}
      <div
        {...attributes}
        {...listeners}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 z-20 cursor-grab active:cursor-grabbing p-1 rounded hover:bg-gray-100 transition-colors"
        style={{ touchAction: 'none' }}
        onClick={e => e.stopPropagation()}
        onMouseDown={e => e.stopPropagation()}
      >
        <GripVertical size={16} className="text-gray-400 hover:text-gray-600" />
      </div>

      {/* Content with left padding to make room for drag handle */}
      <div className="pl-8">{children}</div>
    </div>
  );
}
