import React from 'react';

import { Target } from '~/shared/types/Target';

import { TargetsList } from './TargetsList';

export interface SortableTargetsListProps {
  selectedData: Target[];
  isDraggingNewItem?: boolean;
  children: React.ReactNode;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant: 'primary' | 'secondary';
    iconLeft?: React.ReactNode;
    iconRight?: React.ReactNode;
    disabled?: boolean;
  }>;
  isReorderingDisabled?: boolean;
}

export function SortableTargetsList({
  selectedData,
  isDraggingNewItem = false,
  actions,
  children,
  isReorderingDisabled = false,
}: SortableTargetsListProps) {
  // Only enable sortable context when not dragging new items and reordering is enabled
  const shouldEnableSorting = !isDraggingNewItem && !isReorderingDisabled;

  if (!shouldEnableSorting) {
    // When sorting is disabled, just render the regular TargetsList
    return (
      <TargetsList
        selectedData={selectedData}
        isDraggingNewItem={isDraggingNewItem}
        actions={actions}
      >
        {children}
      </TargetsList>
    );
  }

  return (
    <TargetsList
      selectedData={selectedData}
      isDraggingNewItem={isDraggingNewItem}
      actions={actions}
    >
      {children}
    </TargetsList>
  );
}
